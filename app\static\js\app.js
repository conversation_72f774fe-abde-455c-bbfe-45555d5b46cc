

// PEPE Store - Single Theme System
function initPepeTheme() {
    // PEPE theme is always active - no need for theme switching
    document.documentElement.setAttribute('data-theme', 'dark');
}

// Auto-detect system theme preference
function detectSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark';
    }
    return 'light';
}

// Listen for system theme changes
if (window.matchMedia) {
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
        if (!localStorage.getItem('theme')) {
            const newTheme = e.matches ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', newTheme);
            updateThemeIcon(newTheme);
        }
    });
}

// Form Validation and Enhancement
function enhanceForms() {
    // File upload validation
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function(e) {
            const files = e.target.files;
            const maxSize = 100 * 1024 * 1024; // 100MB

            for (let file of files) {
                if (file.size > maxSize) {
                    alert(`File "${file.name}" is too large. Maximum size is 100MB.`);
                    e.target.value = '';
                    return;
                }
            }

            // Show file names for multiple files
            if (files.length > 1) {
                const fileNames = Array.from(files).map(f => f.name).join(', ');
                const helpText = e.target.nextElementSibling;
                if (helpText && helpText.classList.contains('form-text')) {
                    helpText.textContent = `Selected: ${fileNames}`;
                }
            }
        });
    });

    // Character counters
    const textareas = document.querySelectorAll('textarea[maxlength]');
    textareas.forEach(textarea => {
        const maxLength = parseInt(textarea.getAttribute('maxlength'));
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        counter.style.fontSize = '0.875rem';
        textarea.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - textarea.value.length;
            counter.textContent = `${remaining} characters remaining`;

            if (remaining < 50) {
                counter.classList.add('text-warning');
                counter.classList.remove('text-muted');
            } else {
                counter.classList.add('text-muted');
                counter.classList.remove('text-warning');
            }
        }

        textarea.addEventListener('input', updateCounter);
        updateCounter();
    });
}

// Loading States
function showLoading(element) {
    element.classList.add('loading');
    const originalText = element.textContent;
    element.setAttribute('data-original-text', originalText);
    element.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
    element.disabled = true;
}

function hideLoading(element) {
    element.classList.remove('loading');
    const originalText = element.getAttribute('data-original-text');
    if (originalText) {
        element.textContent = originalText;
        element.removeAttribute('data-original-text');
    }
    element.disabled = false;
}

// Search Enhancement
function enhanceSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;

        searchInput.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);

            // Auto-submit search after 500ms of no typing
            searchTimeout = setTimeout(() => {
                if (e.target.value.length >= 3 || e.target.value.length === 0) {
                    e.target.form.submit();
                }
            }, 500);
        });
    }
}

// Image Loading Enhancement
function enhanceImages() {
    const images = document.querySelectorAll('img[data-src]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback for browsers without IntersectionObserver
        images.forEach(img => {
            img.src = img.dataset.src;
        });
    }
}

// Smooth Scrolling
function enableSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Toast Notifications
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();

    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}

// Keyboard Shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('input[name="search"]');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Ctrl/Cmd + Shift + T for theme toggle
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            toggleTheme();
        }
    });
}

// Analytics and Tracking
function trackEvent(category, action, label = null) {
    // Placeholder for analytics tracking
    // Example: Google Analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', action, {
            event_category: category,
            event_label: label
        });
    }
}

// Error Handling
function setupErrorHandling() {
    window.addEventListener('error', function() {
        // Send to logging service in production
        if (window.location.hostname !== 'localhost') {
            // trackEvent('error', 'javascript', error.message);
        }
    });

    window.addEventListener('unhandledrejection', function() {
        // Send to logging service in production
        if (window.location.hostname !== 'localhost') {
            // trackEvent('error', 'promise_rejection', reason);
        }
    });
}

// Apple Liquid Glass Dynamic Lighting
function initLiquidGlass() {
    // Dynamic lighting based on mouse position
    document.addEventListener('mousemove', function(e) {
        const x = (e.clientX / window.innerWidth) * 100;
        const y = (e.clientY / window.innerHeight) * 100;

        document.documentElement.style.setProperty('--mouse-x', x + '%');
        document.documentElement.style.setProperty('--mouse-y', y + '%');
    });

    // Scroll-based adaptive lighting
    let scrolled = false;
    window.addEventListener('scroll', function() {
        const isScrolled = window.scrollY > 50;
        if (isScrolled !== scrolled) {
            scrolled = isScrolled;
            document.body.classList.toggle('scrolled', scrolled);
        }
    });

    // ===== CLEAN PEPE NAVBAR & DROPDOWN JAVASCRIPT =====

    // Mobile menu toggle
    window.toggleMobileMenu = function() {
        const navbar = document.getElementById('pepeNavbar');
        navbar.classList.toggle('show');
    };

    // User dropdown toggle
    window.toggleUserDropdown = function(event) {
        event.preventDefault();
        event.stopPropagation();

        const dropdown = document.getElementById('userDropdown');
        const dropdownMenu = document.getElementById('userDropdownMenu');
        const toggle = event.currentTarget;

        // Close other dropdowns first
        document.querySelectorAll('.pepe-dropdown.active').forEach(otherDropdown => {
            if (otherDropdown !== dropdown) {
                otherDropdown.classList.remove('active');
                const otherMenu = otherDropdown.querySelector('.pepe-dropdown-menu');
                if (otherMenu) {
                    otherMenu.classList.remove('show');
                }
            }
        });

        // Toggle current dropdown
        const isActive = dropdown.classList.contains('active');

        if (isActive) {
            // Close dropdown
            dropdown.classList.remove('active');
            dropdownMenu.classList.remove('show');
        } else {
            // Open dropdown
            dropdown.classList.add('active');

            // Position dropdown outside navbar
            positionDropdown(dropdownMenu, toggle);

            // Show dropdown after positioning
            setTimeout(() => {
                dropdownMenu.classList.add('show');
            }, 10);
        }
    };

    // Position dropdown function - FIXED MOBILE POSITIONING
    function positionDropdown(dropdownMenu, toggle) {
        const toggleRect = toggle.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const isMobile = viewportWidth <= 768;

        if (isMobile) {
            // Mobile: Position BELOW the button, not at bottom of screen
            const navbarHeight = 70; // navbar height
            const mobileNavHeight = document.querySelector('.pepe-navbar-nav.show') ?
                document.querySelector('.pepe-navbar-nav').offsetHeight : 0;

            let top = navbarHeight + mobileNavHeight + 8; // Below navbar + mobile nav

            // If dropdown is in mobile nav, position below the toggle button
            if (toggle.closest('.pepe-navbar-nav')) {
                top = toggleRect.bottom + 8;
            }

            // Ensure it doesn't go off screen
            if (top + 300 > viewportHeight - 16) {
                top = viewportHeight - 300 - 16;
            }

            dropdownMenu.style.position = 'fixed';
            dropdownMenu.style.top = `${top}px`;
            dropdownMenu.style.bottom = 'auto';
            dropdownMenu.style.left = '1rem';
            dropdownMenu.style.right = '1rem';
            dropdownMenu.style.width = 'auto';
            dropdownMenu.style.minWidth = 'auto';
            dropdownMenu.style.maxWidth = 'none';
            dropdownMenu.style.zIndex = '9999';
        } else {
            // Desktop: Below toggle button, right-aligned
            const dropdownWidth = 240;
            let top = toggleRect.bottom + 8;
            let left = toggleRect.right - dropdownWidth;

            // Prevent going off screen
            if (left < 8) {
                left = 8;
            }
            if (left + dropdownWidth > viewportWidth - 8) {
                left = viewportWidth - dropdownWidth - 8;
            }
            if (top + 300 > viewportHeight - 8) {
                top = toggleRect.top - 300 - 8;
            }

            dropdownMenu.style.position = 'fixed';
            dropdownMenu.style.top = `${top}px`;
            dropdownMenu.style.left = `${left}px`;
            dropdownMenu.style.right = 'auto';
            dropdownMenu.style.bottom = 'auto';
            dropdownMenu.style.width = 'auto';
            dropdownMenu.style.minWidth = `${dropdownWidth}px`;
            dropdownMenu.style.zIndex = '9999';
        }
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.pepe-dropdown')) {
            document.querySelectorAll('.pepe-dropdown.active').forEach(dropdown => {
                dropdown.classList.remove('active');
                const menu = dropdown.querySelector('.pepe-dropdown-menu');
                if (menu) {
                    menu.classList.remove('show');
                }
            });
        }

        // Close mobile menu when clicking outside
        if (!e.target.closest('.pepe-navbar') && !e.target.closest('.pepe-navbar-toggle')) {
            const navbar = document.getElementById('pepeNavbar');
            if (navbar) {
                navbar.classList.remove('show');
            }
        }
    });

    // Reposition dropdowns on window resize
    window.addEventListener('resize', function() {
        document.querySelectorAll('.pepe-dropdown.active').forEach(dropdown => {
            const menu = dropdown.querySelector('.pepe-dropdown-menu');
            const toggle = dropdown.querySelector('.pepe-dropdown-toggle');
            if (menu && toggle) {
                positionDropdown(menu, toggle);
            }
        });
    });

    // Close dropdowns on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            document.querySelectorAll('.pepe-dropdown.active').forEach(dropdown => {
                dropdown.classList.remove('active');
                const menu = dropdown.querySelector('.pepe-dropdown-menu');
                if (menu) {
                    menu.classList.remove('show');
                }
            });

            const navbar = document.getElementById('pepeNavbar');
            if (navbar) {
                navbar.classList.remove('show');
            }
        }
    });
}

// Description expand/collapse functionality
function initDescriptionToggle() {
    const expandBtn = document.getElementById('expandDescBtn');
    const descriptionContent = document.getElementById('descriptionContent');

    if (expandBtn && descriptionContent) {
        expandBtn.addEventListener('click', function() {
            const isExpanded = descriptionContent.classList.contains('expanded');

            if (isExpanded) {
                // Collapse
                descriptionContent.classList.remove('expanded');
                expandBtn.innerHTML = '<i class="bi bi-arrows-expand"></i><span class="expand-text">Expand</span>';
                expandBtn.setAttribute('aria-label', 'Expand description');

                // Smooth scroll to top of description
                descriptionContent.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            } else {
                // Expand
                descriptionContent.classList.add('expanded');
                expandBtn.innerHTML = '<i class="bi bi-arrows-collapse"></i><span class="expand-text">Collapse</span>';
                expandBtn.setAttribute('aria-label', 'Collapse description');
            }
        });

        // Check if content needs expand button
        const contentHeight = descriptionContent.scrollHeight;
        const visibleHeight = 400; // Max height when collapsed

        if (contentHeight <= visibleHeight) {
            expandBtn.style.display = 'none';
        }
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize PEPE theme
    initPepeTheme();

    // Initialize Apple Liquid Glass
    initLiquidGlass();

    // Initialize description toggle
    initDescriptionToggle();

    // Initialize other features
    enhanceForms();
    enhanceSearch();
    enhanceImages();
    enableSmoothScrolling();
    setupKeyboardShortcuts();
    setupErrorHandling();

    // Add fade-in animation to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }
});

// Export functions for global use
window.showToast = showToast;
window.trackEvent = trackEvent;
