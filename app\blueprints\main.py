"""
Main blueprint for public routes
"""
import os
import uuid
import hmac
import hashlib
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, redirect, url_for, flash, session, send_file, jsonify, abort, make_response

from app.models import App, Screenshot, AppRating, Fingerprint, AbuseReport, Suggestion, Shortlink, DownloadLog, get_db
from app.utils.common import sanitize_input, get_client_ip, AppList
from app.utils.auth import check_cookie_auth
from app.fp import validate_fingerprint_header
from app.ban import check_fingerprint_ban
from app.referral import handle_referral_parameter, track_referral

main_bp = Blueprint('main', __name__)

# Register the before_request handler for this blueprint
@main_bp.before_request
def check_auth():
    """Check authentication and fingerprint validation for all requests"""
    # First check if fingerprint is banned
    ban_result = check_fingerprint_ban()
    if ban_result:
        return ban_result

    # Then validate fingerprint headers
    fp_result = validate_fingerprint_header()
    if fp_result:
        return fp_result

    # Finally check cookie authentication
    return check_cookie_auth()


# Register the after_request handler for referral tracking
@main_bp.after_request
def handle_referrals(response):
    """Handle referral parameter and tracking after request"""
    # Handle ?ref= parameter and set cookie
    ref_response = handle_referral_parameter()
    if ref_response:
        # Copy cookies from referral response to main response
        for cookie in ref_response.headers.getlist('Set-Cookie'):
            response.headers.add('Set-Cookie', cookie)

    # Track referral if conditions are met
    track_referral()

    return response


@main_bp.route('/')
def index():
    """Main page showing apps"""
    try:
        page = max(1, request.args.get('page', 1, type=int))
        category = request.args.get('category', '').strip()
        search = request.args.get('search', '').strip()

        # Get apps with pagination
        per_page = 12
        offset = (page - 1) * per_page

        # Get total count for pagination
        total_apps = App.get_count(category=category, search=search)

        # Calculate pagination variables
        has_prev = page > 1
        has_next = offset + per_page < total_apps

        # Get paginated apps
        apps_page = App.get_all(limit=per_page, offset=offset, category=category, search=search)

        apps = AppList(apps_page, total_apps, page, per_page, has_prev, has_next)

        # Get featured apps (limit to avoid performance issues)
        featured_apps = App.get_featured(limit=6)
        categories = App.get_categories()

        return render_template('index.html',
                             apps=apps,
                             featured_apps=featured_apps,
                             categories=categories,
                             current_category=category,
                             search_term=search,
                             page=page,
                             has_prev=has_prev,
                             has_next=has_next)

    except Exception as e:
        flash(f'Error loading page: {str(e)}', 'error')
        # Return empty results on error
        return render_template('index.html',
                             apps=type('AppList', (), {'items': [], 'total': 0, 'pages': 1, 'page': 1, 'has_prev': False, 'has_next': False, 'iter_pages': lambda: [1]})(),
                             featured_apps=[],
                             categories=[],
                             current_category='',
                             search_term='',
                             page=1,
                             has_prev=False,
                             has_next=False)


@main_bp.route('/gate')
def gate():
    """Gate page for fingerprinting"""
    # Get the return URL from query parameters
    next_url = request.args.get('next', '/')

    # Decode the URL if it was encoded
    from urllib.parse import unquote
    next_url = unquote(next_url)

    # Security validation: only allow safe internal redirects
    safe_next_url = validate_redirect_url(next_url)

    return render_template('gate.html', next_url=safe_next_url)


def validate_redirect_url(url):
    """
    Validate redirect URL to prevent open redirect vulnerabilities.
    Only allows internal app routes, not user-provided URLs.
    """
    if not url or url == '/':
        return '/'

    # Remove any leading/trailing whitespace
    url = url.strip()

    # Block any absolute URLs (http://, https://, ftp://, etc.)
    if '://' in url:
        return '/'

    # Block URLs that start with // (protocol-relative URLs)
    if url.startswith('//'):
        return '/'

    # Block URLs with @ symbol (could be used for credential injection)
    if '@' in url:
        return '/'

    # Block URLs with backslashes (Windows path traversal)
    if '\\' in url:
        return '/'

    # Ensure URL starts with /
    if not url.startswith('/'):
        url = '/' + url

    # Define allowed internal routes (whitelist approach)
    allowed_routes = [
        '/app/',           # App detail pages
        '/category/',      # Category pages
        '/search',         # Search pages
        '/suggestions',    # Suggestions page
        '/admin/',         # Admin pages (will require login)
        '/publisher/',     # Publisher pages (will require login)
        '/auth/',          # Auth pages
    ]

    # Check if URL starts with any allowed route
    is_allowed = any(url.startswith(route) for route in allowed_routes)

    # Also allow exact matches for specific pages
    exact_allowed = ['/', '/about', '/contact', '/privacy', '/terms']
    if url in exact_allowed:
        is_allowed = True

    # If not in whitelist, redirect to home
    if not is_allowed:
        return '/'

    # Additional security: limit URL length to prevent abuse
    if len(url) > 200:
        return '/'

    # Block URLs with too many path segments (potential traversal)
    path_segments = url.split('/')
    if len(path_segments) > 10:
        return '/'

    # Block URLs with suspicious patterns
    suspicious_patterns = [
        '../',           # Path traversal
        '..\\',          # Windows path traversal
        '%2e%2e',        # URL encoded ..
        '%2f',           # URL encoded /
        '%5c',           # URL encoded \
        'javascript:',   # JavaScript injection
        'data:',         # Data URLs
        'vbscript:',     # VBScript injection
    ]

    url_lower = url.lower()
    for pattern in suspicious_patterns:
        if pattern in url_lower:
            return '/'

    return url


@main_bp.route('/registerf', methods=['POST'])
def registerf():
    """Register fingerprint"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        fingerprint = data.get('fingerprint', '').strip()
        browser_data = str(data.get('browser_data', ''))

        if not fingerprint:
            return jsonify({'success': False, 'error': 'Fingerprint is required'}), 400

        # Check if fingerprint exists
        fp_data = Fingerprint.get_by_fingerprint(fingerprint)
        if fp_data:
            user_uuid = fp_data['uuid']
            hmac_key = fp_data['hmac_key']
        else:
            # Create new fingerprint
            user_uuid = str(uuid.uuid4())
            hmac_key = uuid.uuid4().hex
            try:
                Fingerprint.create(user_uuid, fingerprint, hmac_key, browser_data)
            except Exception as e:
                return jsonify({'success': False, 'error': 'Failed to create fingerprint'}), 500

        # Create signature
        sig = hmac.new(hmac_key.encode(), f"{user_uuid}:{fingerprint}".encode(), hashlib.sha256).hexdigest()

        resp = make_response(jsonify({'success': True}))
        resp.set_cookie('uuid', user_uuid, httponly=True, max_age=30*24*60*60)  # 30 days
        resp.set_cookie('fp', fingerprint, httponly=True, max_age=30*24*60*60)
        resp.set_cookie('sig', sig, httponly=True, max_age=30*24*60*60)
        return resp

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@main_bp.route('/app/<int:app_id>')
def app_detail(app_id):
    """App detail page"""
    try:
        if app_id <= 0:
            abort(404)

        app = App.get_by_id(app_id)
        if not app:
            abort(404)

        screenshots = Screenshot.get_by_app_id(app_id)
        ratings = AppRating.get_by_app_id(app_id, limit=10)  # Limit to recent 10 ratings

        # Calculate rating stats
        avg_rating = App.get_average_rating(app_id)
        rating_count = App.get_rating_count(app_id)
        rating_distribution = AppRating.get_rating_distribution(app_id)

        # Check if current user has rated this app and can edit
        user_rating_id = None
        can_edit_rating = False
        edit_time_remaining = None

        if session.get('user_id'):
            user_rating_id = AppRating.user_has_rated(app_id, session['user_id'])
        else:
            # Check by IP for anonymous users
            user_rating_id = AppRating.user_has_rated(app_id, ip_address=get_client_ip())

        if user_rating_id:
            # Check if user can edit their rating
            can_edit, message = AppRating.can_edit_rating(
                user_rating_id,
                session.get('user_id'),
                get_client_ip()
            )
            can_edit_rating = can_edit

            # Calculate time remaining for editing
            if can_edit:
                with get_db() as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT timestamp FROM app_ratings WHERE id = ?', (user_rating_id,))
                    row = cursor.fetchone()
                    if row:
                        timestamp_str = row[0]
                        try:
                            if isinstance(timestamp_str, str):
                                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                            else:
                                timestamp = timestamp_str

                            time_limit = timestamp + timedelta(hours=AppRating.EDIT_TIME_LIMIT_HOURS)
                            remaining = time_limit - datetime.now()
                            if remaining.total_seconds() > 0:
                                edit_time_remaining = {
                                    'hours': int(remaining.total_seconds() // 3600),
                                    'minutes': int((remaining.total_seconds() % 3600) // 60)
                                }
                        except:
                            pass

        app['screenshots'] = screenshots

        return render_template('app_detail.html',
                             app=app,
                             screenshots=screenshots,
                             ratings=ratings,
                             avg_rating=avg_rating,
                             rating_count=rating_count,
                             rating_distribution=rating_distribution,
                             user_has_rated=bool(user_rating_id),
                             user_rating_id=user_rating_id,
                             can_edit_rating=can_edit_rating,
                             edit_time_remaining=edit_time_remaining)
    except Exception as e:
        flash(f'Error loading app details: {str(e)}', 'error')
        return redirect(url_for('main.index'))


@main_bp.route('/uploads/<path:filename>')
def uploaded_file(filename):
    """Serve uploaded files (icons, screenshots, etc.)"""
    try:
        # Normalize filename to use OS-appropriate separators
        from app.config import Config
        normalized_filename = filename.replace('/', os.sep)
        file_path = os.path.abspath(os.path.join(Config.UPLOAD_FOLDER, normalized_filename))

        # Security check - ensure file is within upload directory
        upload_folder_abs = os.path.abspath(Config.UPLOAD_FOLDER)
        if not file_path.startswith(upload_folder_abs):
            abort(403)

        if os.path.exists(file_path):
            return send_file(file_path)
        else:
            abort(404)

    except Exception as e:
        abort(404)


@main_bp.route('/suggestions', methods=['GET', 'POST'])
def suggestions():
    """Handle suggestions"""
    if request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            category = sanitize_input(data.get('category', ''))
            title = sanitize_input(data.get('title', ''))
            description = sanitize_input(data.get('description', ''))
            priority = sanitize_input(data.get('priority', 'normal'))

            # Validate required fields
            if not category or not title or not description:
                return jsonify({'success': False, 'error': 'Category, title, and description are required'}), 400

            # Validate category
            valid_categories = Suggestion.get_categories()
            if category not in valid_categories:
                return jsonify({'success': False, 'error': 'Invalid category'}), 400

            # Create suggestion
            suggestion_id = Suggestion.create(
                category=category,
                title=title,
                description=description,
                user_id=session.get('user_id'),
                ip_address=get_client_ip(),
                user_agent=request.headers.get('User-Agent', ''),
                priority=priority
            )

            if suggestion_id:
                return jsonify({
                    'success': True,
                    'message': 'Suggestion submitted successfully. Thank you for your feedback!',
                    'suggestion_id': suggestion_id
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to submit suggestion'}), 500

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    else:
        # GET request - show suggestions page
        try:
            categories = Suggestion.get_categories()
            return render_template('suggestions.html', categories=categories)
        except Exception as e:
            flash(f'Error loading suggestions page: {str(e)}', 'error')
            return redirect(url_for('main.index'))


@main_bp.route('/download/<int:app_id>')
def download_app(app_id):
    """Download app file"""
    try:
        app = App.get_by_id(app_id)
        if not app:
            flash('App not found', 'error')
            return redirect(url_for('main.index'))

        # Check if it's an external download
        if app.get('external_url'):
            # Log the download
            DownloadLog.create(app_id, get_client_ip(), request.headers.get('User-Agent', ''))
            # Increment download count
            App.increment_downloads(app_id)
            # Redirect to external URL
            return redirect(app['external_url'])

        # Handle file download
        if not app.get('file_path'):
            flash('No file available for download', 'error')
            return redirect(url_for('main.app_detail', app_id=app_id))

        from app.config import Config
        file_path = os.path.join(Config.UPLOAD_FOLDER, app['file_path'])

        if not os.path.exists(file_path):
            flash('File not found', 'error')
            return redirect(url_for('main.app_detail', app_id=app_id))

        # Log the download
        DownloadLog.create(app_id, get_client_ip(), request.headers.get('User-Agent', ''))

        # Increment download count
        App.increment_downloads(app_id)

        # Send file
        return send_file(file_path, as_attachment=True, download_name=f"{app['name']}_v{app['version']}.{app['file_path'].split('.')[-1]}")

    except Exception as e:
        flash(f'Download error: {str(e)}', 'error')

    return redirect(url_for('main.app_detail', app_id=app_id))


@main_bp.route('/app/<int:app_id>/rate', methods=['POST'])
def rate_app(app_id):
    """Rate an app or edit existing rating (form submission)"""
    try:
        if app_id <= 0:
            flash('Invalid app ID', 'error')
            return redirect(url_for('main.app_detail', app_id=app_id))

        rating = request.form.get('rating')
        review = sanitize_input(request.form.get('review', ''))
        edit_reason = sanitize_input(request.form.get('edit_reason', ''))
        is_edit = request.form.get('is_edit') == 'true'

        # Validate rating
        try:
            rating = int(rating)
            if rating < 1 or rating > 5:
                flash('Rating must be between 1 and 5', 'error')
                return redirect(url_for('main.app_detail', app_id=app_id))
        except (ValueError, TypeError):
            flash('Invalid rating value', 'error')
            return redirect(url_for('main.app_detail', app_id=app_id))

        # Check if app exists
        app = App.get_by_id(app_id)
        if not app:
            flash('App not found', 'error')
            return redirect(url_for('main.index'))

        # Check if user has already rated this app
        user_rating_id = None
        if session.get('user_id'):
            user_rating_id = AppRating.user_has_rated(app_id, session['user_id'])
        else:
            # Check by IP for anonymous users
            user_rating_id = AppRating.user_has_rated(app_id, ip_address=get_client_ip())

        if is_edit and user_rating_id:
            # Double-check if user can still edit (in case time limit passed during form submission)
            can_edit, edit_message = AppRating.can_edit_rating(
                user_rating_id,
                session.get('user_id'),
                get_client_ip()
            )

            if not can_edit:
                flash(f'Cannot edit rating: {edit_message}', 'error')
                return redirect(url_for('main.app_detail', app_id=app_id))

            # Update existing rating
            success, message = AppRating.update_rating(
                user_rating_id,
                rating,
                review,
                session.get('user_id'),
                get_client_ip(),
                edit_reason
            )

            if success:
                flash('Your rating has been updated!', 'success')
            else:
                flash(f'Failed to update rating: {message}', 'error')

        elif not user_rating_id:
            # Create new rating
            rating_id = AppRating.create(
                app_id=app_id,
                rating=rating,
                review=review,
                user_id=session.get('user_id'),
                ip_address=get_client_ip()
            )

            if rating_id:
                flash('Thank you for your rating!', 'success')
            else:
                flash('Failed to submit rating', 'error')
        else:
            flash('You have already rated this app', 'warning')

        return redirect(url_for('main.app_detail', app_id=app_id))

    except Exception as e:
        flash(f'Error submitting rating: {str(e)}', 'error')
        return redirect(url_for('main.app_detail', app_id=app_id))


@main_bp.route('/app/<int:app_id>/report', methods=['POST'])
def report_abuse(app_id):
    """Report abuse for an app"""
    try:
        if app_id <= 0:
            return jsonify({'success': False, 'error': 'Invalid app ID'}), 400

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        report_type = sanitize_input(data.get('report_type', ''))
        reason = sanitize_input(data.get('reason', ''))
        description = sanitize_input(data.get('description', ''))

        # Validate required fields
        if not report_type or not reason:
            return jsonify({'success': False, 'error': 'Report type and reason are required'}), 400

        # Check if app exists
        app = App.get_by_id(app_id)
        if not app:
            return jsonify({'success': False, 'error': 'App not found'}), 404

        # Create abuse report
        report_id = AbuseReport.create(
            app_id=app_id,
            report_type=report_type,
            reason=reason,
            description=description,
            user_id=session.get('user_id'),
            ip_address=get_client_ip(),
            user_agent=request.headers.get('User-Agent', '')
        )

        if report_id:
            return jsonify({
                'success': True,
                'message': 'Report submitted successfully. Thank you for helping keep our platform safe.',
                'report_id': report_id
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to submit report'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@main_bp.route('/s/<short_code>')
def shortlink_redirect(short_code):
    """Redirect shortlink"""
    try:
        shortlink = Shortlink.get_by_code(short_code)
        if not shortlink:
            abort(404)

        # Check if expired
        if shortlink.get('expires_at'):
            try:
                expires_at = datetime.strptime(shortlink['expires_at'], '%Y-%m-%d %H:%M:%S')
                if datetime.now() > expires_at:
                    abort(404)
            except:
                pass

        # Record click
        Shortlink.increment_clicks(
            shortlink['id'],
            get_client_ip(),
            request.headers.get('User-Agent', ''),
            request.headers.get('Referer', '')
        )

        # Redirect to original URL
        return redirect(shortlink['original_url'])

    except Exception as e:
        abort(404)



