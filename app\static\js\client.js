/**
 * Iron-clad Fingerprinting Client
 * Collects 20+ entropy signals and sends raw data to server
 * Implements nonce-based replay defense and tamper-proof headers
 */

class SecureFingerprinter {
  constructor() {
    this.currentNonce = null;
    this.fingerprint = null;
  }

  async collectEntropyData() {
    const entropy = {};

    try {
      // Canvas fingerprinting
      entropy.canvas_hash = await this.getCanvasHash();

      // WebGL fingerprinting
      entropy.webgl_renderer = this.getWebGLRenderer();

      // Audio context fingerprinting
      entropy.audio_context = await this.getAudioContextHash();

      // Screen and display info
      entropy.screen_resolution = `${screen.width}x${screen.height}`;
      entropy.screen_color_depth = screen.colorDepth;
      entropy.screen_pixel_ratio = window.devicePixelRatio || 1;

      // Timezone and locale
      entropy.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      entropy.timezone_offset = new Date().getTimezoneOffset();
      entropy.language = navigator.language;
      entropy.languages = navigator.languages ? navigator.languages.join(',') : '';

      // Platform and hardware
      entropy.platform = navigator.platform;
      entropy.user_agent = navigator.userAgent;
      entropy.cpu_cores = navigator.hardwareConcurrency || 0;
      entropy.device_memory = navigator.deviceMemory || 0;

      // Touch and input capabilities
      entropy.touch_support = 'ontouchstart' in window;
      entropy.max_touch_points = navigator.maxTouchPoints || 0;
      entropy.pointer_type = this.getPointerType();

      // Font fingerprinting
      entropy.fonts_hash = this.getFontsHash();

      // Media capabilities
      entropy.media_capabilities = await this.getMediaCapabilities();

      // Battery (if available)
      if ('getBattery' in navigator) {
        try {
          const battery = await navigator.getBattery();
          entropy.battery_level = Math.round(battery.level * 100);
        } catch (e) {
          entropy.battery_level = -1;
        }
      } else {
        entropy.battery_level = -1;
      }

      // Connection info
      if ('connection' in navigator) {
        entropy.connection_type = navigator.connection.effectiveType || 'unknown';
      } else {
        entropy.connection_type = 'unknown';
      }

      // Vendor and rendering info
      entropy.vendor = navigator.vendor || '';
      entropy.renderer = this.getRenderer();

      // Additional entropy sources
      entropy.do_not_track = navigator.doNotTrack || 'unknown';
      entropy.cookie_enabled = navigator.cookieEnabled;
      entropy.java_enabled = navigator.javaEnabled ? navigator.javaEnabled() : false;

    } catch (error) {
      console.warn('Error collecting entropy:', error);
    }

    return entropy;
  }

  async getCanvasHash() {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = 200;
      canvas.height = 50;

      // Draw complex pattern for fingerprinting
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillStyle = '#f60';
      ctx.fillRect(125, 1, 62, 20);
      ctx.fillStyle = '#069';
      ctx.fillText('🔒 Secure Fingerprint 🔒', 2, 15);
      ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
      ctx.fillText('Canvas fingerprint', 4, 35);

      // Add some geometric shapes
      ctx.globalCompositeOperation = 'multiply';
      ctx.fillStyle = 'rgb(255,0,255)';
      ctx.beginPath();
      ctx.arc(50, 25, 20, 0, Math.PI * 2, true);
      ctx.closePath();
      ctx.fill();

      const dataURL = canvas.toDataURL();
      return this.hashString(dataURL);
    } catch (e) {
      return 'canvas_error';
    }
  }

  getWebGLRenderer() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) return 'no_webgl';

      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      if (debugInfo) {
        return gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) || 'unknown_renderer';
      }
      return 'no_debug_info';
    } catch (e) {
      return 'webgl_error';
    }
  }

  async getAudioContextHash() {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const analyser = audioContext.createAnalyser();
      const gainNode = audioContext.createGain();
      const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);

      oscillator.type = 'triangle';
      oscillator.frequency.setValueAtTime(10000, audioContext.currentTime);

      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      oscillator.connect(analyser);
      analyser.connect(scriptProcessor);
      scriptProcessor.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.start(0);

      return new Promise((resolve) => {
        let samples = [];
        scriptProcessor.onaudioprocess = (event) => {
          const sample = event.inputBuffer.getChannelData(0)[0];
          if (sample) samples.push(sample);
          if (samples.length > 1000) {
            oscillator.stop();
            audioContext.close();
            resolve(this.hashString(samples.join('')));
          }
        };

        setTimeout(() => {
          oscillator.stop();
          audioContext.close();
          resolve('audio_timeout');
        }, 1000);
      });
    } catch (e) {
      return 'audio_error';
    }
  }

  getPointerType() {
    if ('PointerEvent' in window) {
      return 'pointer_events';
    } else if ('TouchEvent' in window) {
      return 'touch_events';
    } else {
      return 'mouse_only';
    }
  }

  getFontsHash() {
    const testFonts = [
      'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
      'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
      'Trebuchet MS', 'Arial Black', 'Impact', 'Lucida Console',
      'Tahoma', 'Geneva', 'Lucida Sans Unicode', 'Franklin Gothic Medium',
      'Arial Narrow', 'Brush Script MT'
    ];

    const availableFonts = [];
    const testString = 'mmmmmmmmmmlli';
    const testSize = '72px';
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    // Get baseline measurements
    context.font = testSize + ' monospace';
    const baselineWidth = context.measureText(testString).width;

    for (const font of testFonts) {
      context.font = testSize + ' ' + font + ', monospace';
      const width = context.measureText(testString).width;
      if (width !== baselineWidth) {
        availableFonts.push(font);
      }
    }

    return this.hashString(availableFonts.join(','));
  }

  async getMediaCapabilities() {
    const capabilities = {};

    try {
      if ('mediaCapabilities' in navigator) {
        const videoConfig = {
          type: 'file',
          video: {
            contentType: 'video/mp4; codecs="avc1.42E01E"',
            width: 1920,
            height: 1080,
            bitrate: 2646242,
            framerate: 30
          }
        };

        const audioConfig = {
          type: 'file',
          audio: {
            contentType: 'audio/mp4; codecs="mp4a.40.2"',
            channels: 2,
            bitrate: 132266,
            samplerate: 5200
          }
        };

        const [videoSupport, audioSupport] = await Promise.all([
          navigator.mediaCapabilities.decodingInfo(videoConfig).catch(() => null),
          navigator.mediaCapabilities.decodingInfo(audioConfig).catch(() => null)
        ]);

        capabilities.video_smooth = videoSupport ? videoSupport.smooth : false;
        capabilities.video_power_efficient = videoSupport ? videoSupport.powerEfficient : false;
        capabilities.audio_smooth = audioSupport ? audioSupport.smooth : false;
        capabilities.audio_power_efficient = audioSupport ? audioSupport.powerEfficient : false;
      }
    } catch (e) {
      capabilities.error = 'media_capabilities_error';
    }

    return capabilities;
  }

  getRenderer() {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      return ctx ? 'canvas_2d' : 'no_canvas';
    } catch (e) {
      return 'renderer_error';
    }
  }

  hashString(str) {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  async getNonce() {
    try {
      const response = await fetch('/fp/nonce');
      const data = await response.json();
      return data.nonce;
    } catch (e) {
      console.error('Failed to get nonce:', e);
      return null;
    }
  }

  async initialize() {
    try {
      // Get nonce first
      this.currentNonce = await this.getNonce();
      if (!this.currentNonce) {
        throw new Error('Failed to obtain nonce');
      }

      // Collect entropy data
      const entropyData = await this.collectEntropyData();

      // Send to server for fingerprint generation
      const response = await fetch('/fp/collect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          entropy_data: entropyData,
          nonce: this.currentNonce
        })
      });

      const result = await response.json();

      if (result.success) {
        this.fingerprint = result.fingerprint;
        this.setupHeaderInterceptor();
        return true;
      } else {
        console.error('Fingerprinting failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('Fingerprinting initialization error:', error);
      return false;
    }
  }

  setupHeaderInterceptor() {
    // Intercept all fetch requests to add fingerprint header
    const originalFetch = window.fetch;
    const fingerprint = this.fingerprint;

    window.fetch = function(url, options = {}) {
      options.headers = options.headers || {};
      options.headers['X-Visitor-FP'] = fingerprint;
      return originalFetch(url, options);
    };

    // Intercept XMLHttpRequest
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function() {
      originalOpen.apply(this, arguments);
      this.setRequestHeader('X-Visitor-FP', fingerprint);
    };
  }
}

// Auto-initialize when DOM is ready
(async () => {
  try {
    const fingerprinter = new SecureFingerprinter();
    const success = await fingerprinter.initialize();

    if (success) {
      // Stop progress animation if available
      if (window.stopProgress) {
        window.stopProgress();
      }

      // Get the next URL from the global variable set by the template
      const nextUrl = window.NEXT_URL || '/';

      // Small delay to show completion
      setTimeout(() => {
        window.location.href = nextUrl;
      }, 500);
    } else {
      alert("Fingerprint verification failed. Please try again.");
      window.location.href = "/";
    }
  } catch (error) {
    console.error('Fingerprinting error:', error);
    alert("An error occurred during verification. Please try again.");
    window.location.href = "/";
  }
})();
