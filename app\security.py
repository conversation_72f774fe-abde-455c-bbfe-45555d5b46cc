"""
Message Integrity & Transport Security
Implements:
- End-to-end message integrity
- CSRF protection
- Nonce validation
- Security headers
- Rate limiting
"""

import hmac
import hashlib
import secrets
import json
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, session, abort, make_response, current_app
from functools import wraps
from app.models import get_db
from app.utils.common import get_client_ip

security_bp = Blueprint('security', __name__)

class SecurityManager:
    """Manages security operations and message integrity"""
    
    @staticmethod
    def generate_csrf_token():
        """Generate a CSRF token"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def validate_csrf_token(token, session_token):
        """Validate CSRF token using constant-time comparison"""
        if not token or not session_token:
            return False
        return hmac.compare_digest(token, session_token)
    
    @staticmethod
    def sign_message(message, secret_key=None):
        """Sign a message with HMAC-SHA256"""
        if secret_key is None:
            secret_key = current_app.config['SECRET_KEY']
        
        if isinstance(message, dict):
            message = json.dumps(message, sort_keys=True)
        
        signature = hmac.new(
            secret_key.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    @staticmethod
    def verify_message_signature(message, signature, secret_key=None):
        """Verify message signature"""
        if secret_key is None:
            secret_key = current_app.config['SECRET_KEY']
        
        expected_signature = SecurityManager.sign_message(message, secret_key)
        return hmac.compare_digest(signature, expected_signature)
    
    @staticmethod
    def create_secure_payload(data, fingerprint=None):
        """Create a secure payload with signature and timestamp"""
        payload = {
            'data': data,
            'timestamp': datetime.now().isoformat(),
            'fingerprint': fingerprint
        }
        
        signature = SecurityManager.sign_message(payload)
        
        return {
            'payload': payload,
            'signature': signature
        }
    
    @staticmethod
    def verify_secure_payload(secure_payload, max_age_minutes=5):
        """Verify a secure payload"""
        try:
            payload = secure_payload.get('payload')
            signature = secure_payload.get('signature')
            
            if not payload or not signature:
                return False, "Missing payload or signature"
            
            # Verify signature
            if not SecurityManager.verify_message_signature(payload, signature):
                return False, "Invalid signature"
            
            # Check timestamp
            timestamp_str = payload.get('timestamp')
            if not timestamp_str:
                return False, "Missing timestamp"
            
            timestamp = datetime.fromisoformat(timestamp_str)
            age = datetime.now() - timestamp
            
            if age > timedelta(minutes=max_age_minutes):
                return False, "Payload expired"
            
            return True, "Valid payload"
            
        except Exception as e:
            return False, f"Payload verification error: {str(e)}"

class CSRFProtection:
    """CSRF protection implementation"""
    
    @staticmethod
    def generate_token():
        """Generate and store CSRF token in session"""
        token = SecurityManager.generate_csrf_token()
        session['csrf_token'] = token
        return token
    
    @staticmethod
    def validate_token(token):
        """Validate CSRF token from request"""
        session_token = session.get('csrf_token')
        return SecurityManager.validate_csrf_token(token, session_token)
    
    @staticmethod
    def require_csrf_token(f):
        """Decorator to require CSRF token validation"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:
                # Get token from header or form data
                token = (request.headers.get('X-CSRF-Token') or 
                        request.form.get('csrf_token') or
                        (request.get_json() or {}).get('csrf_token'))
                
                if not CSRFProtection.validate_token(token):
                    abort(403, "CSRF token validation failed")
            
            return f(*args, **kwargs)
        return decorated_function

class RateLimiter:
    """Rate limiting implementation"""
    
    @staticmethod
    def check_rate_limit(identifier, endpoint, window_minutes=5, max_requests=60):
        """Check if request is within rate limits"""
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Create rate_limits table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rate_limits (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    identifier TEXT NOT NULL,
                    endpoint TEXT NOT NULL,
                    request_count INTEGER DEFAULT 1,
                    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create index if it doesn't exist
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_rate_limits_lookup 
                ON rate_limits(identifier, endpoint, window_start)
            ''')
            
            # Clean up old entries
            cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
            cursor.execute('DELETE FROM rate_limits WHERE window_start < ?', (cutoff_time,))
            
            # Check current rate
            cursor.execute('''
                SELECT SUM(request_count) FROM rate_limits 
                WHERE identifier = ? AND endpoint = ? AND window_start >= ?
            ''', (identifier, endpoint, cutoff_time))
            
            result = cursor.fetchone()
            current_count = result[0] if result[0] else 0
            
            if current_count >= max_requests:
                return False, f"Rate limit exceeded: {current_count}/{max_requests}"
            
            # Add this request
            cursor.execute('''
                INSERT INTO rate_limits (identifier, endpoint) 
                VALUES (?, ?)
            ''', (identifier, endpoint))
            
            conn.commit()
            return True, f"Rate limit OK: {current_count + 1}/{max_requests}"
    
    @staticmethod
    def rate_limit(window_minutes=5, max_requests=60, per='ip'):
        """Decorator for rate limiting"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                # Determine identifier
                if per == 'ip':
                    identifier = get_client_ip()
                elif per == 'fingerprint':
                    identifier = (request.cookies.get('fingerprint') or 
                                request.headers.get('X-Visitor-FP') or 
                                get_client_ip())
                else:
                    identifier = get_client_ip()
                
                endpoint = request.endpoint or 'unknown'
                
                allowed, message = RateLimiter.check_rate_limit(
                    identifier, endpoint, window_minutes, max_requests
                )
                
                if not allowed:
                    return jsonify({'error': message}), 429
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator

class SecurityHeaders:
    """Security headers management"""
    
    @staticmethod
    def add_security_headers(response):
        """Add comprehensive security headers to response"""
        # HTTPS enforcement
        response.headers['Strict-Transport-Security'] = (
            'max-age=31536000; includeSubDomains; preload'
        )
        
        # Content Security Policy
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' "
            "https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
            "style-src 'self' 'unsafe-inline' "
            "https://cdn.jsdelivr.net https://fonts.googleapis.com; "
            "font-src 'self' https://fonts.gstatic.com; "
            "img-src 'self' data: https:; "
            "connect-src 'self'; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self'"
        )
        response.headers['Content-Security-Policy'] = csp
        
        # Other security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response.headers['Permissions-Policy'] = (
            'geolocation=(), microphone=(), camera=(), '
            'payment=(), usb=(), magnetometer=(), gyroscope=()'
        )
        
        # Remove server information
        response.headers.pop('Server', None)
        
        return response

# API Routes
@security_bp.route('/api/csrf-token')
def get_csrf_token():
    """Get CSRF token for client use"""
    token = CSRFProtection.generate_token()
    return jsonify({'csrf_token': token})

@security_bp.route('/api/secure-ping', methods=['POST'])
@CSRFProtection.require_csrf_token
@RateLimiter.rate_limit(window_minutes=1, max_requests=10, per='fingerprint')
def secure_ping():
    """Test endpoint for secure communication"""
    data = request.get_json() or {}
    fingerprint = (request.cookies.get('fingerprint') or 
                  request.headers.get('X-Visitor-FP'))
    
    # Create secure response
    response_data = {
        'message': 'Secure ping successful',
        'timestamp': datetime.now().isoformat(),
        'fingerprint_present': bool(fingerprint)
    }
    
    secure_payload = SecurityManager.create_secure_payload(response_data, fingerprint)
    
    return jsonify({
        'success': True,
        'secure_payload': secure_payload
    })

@security_bp.route('/api/verify-payload', methods=['POST'])
@CSRFProtection.require_csrf_token
def verify_payload():
    """Verify a secure payload"""
    data = request.get_json()
    if not data or 'secure_payload' not in data:
        return jsonify({'success': False, 'error': 'Missing secure payload'}), 400
    
    secure_payload = data['secure_payload']
    is_valid, message = SecurityManager.verify_secure_payload(secure_payload)
    
    return jsonify({
        'success': is_valid,
        'message': message,
        'payload_data': secure_payload.get('payload', {}).get('data') if is_valid else None
    })

# Middleware functions
def add_security_headers_middleware():
    """Middleware to add security headers to all responses"""
    def after_request(response):
        return SecurityHeaders.add_security_headers(response)
    return after_request

def validate_content_type():
    """Middleware to validate content type for API requests"""
    if (request.endpoint and 
        request.endpoint.startswith('api.') and 
        request.method in ['POST', 'PUT', 'PATCH']):
        
        if not request.is_json:
            abort(400, "Content-Type must be application/json")
    
    return None

def enforce_https():
    """Middleware to enforce HTTPS in production"""
    if (not request.is_secure and 
        not current_app.debug and 
        request.headers.get('X-Forwarded-Proto') != 'https'):
        
        # Redirect to HTTPS
        url = request.url.replace('http://', 'https://', 1)
        return redirect(url, code=301)
    
    return None

# Utility functions for integration
def init_security_middleware(app):
    """Initialize security middleware for the Flask app"""
    
    @app.before_request
    def security_before_request():
        """Security checks before each request"""
        # Enforce HTTPS
        https_result = enforce_https()
        if https_result:
            return https_result
        
        # Validate content type
        content_type_result = validate_content_type()
        if content_type_result:
            return content_type_result
        
        return None
    
    @app.after_request
    def security_after_request(response):
        """Add security headers after each request"""
        return SecurityHeaders.add_security_headers(response)
    
    return app
